// 简单的测试脚本来验证 FilePathResolver 功能
const path = require('path');
const fs = require('fs');

// 模拟 FilePathResolver 的核心逻辑
class TestFilePathResolver {
  static STATIC_DIR = '.automation/static';

  static resolveFilePath(filename) {
    if (!filename) {
      throw new Error('文件名不能为空');
    }

    if (filename.includes('/') || filename.includes('\\')) {
      throw new Error(`文件名不能包含路径分隔符: ${filename}`);
    }

    const fullPath = path.join(process.cwd(), this.STATIC_DIR, filename);

    if (!fs.existsSync(fullPath)) {
      throw new Error(`文件不存在: ${filename}，请确保文件已放置在 ${this.STATIC_DIR} 目录中`);
    }

    const stats = fs.statSync(fullPath);
    if (!stats.isFile()) {
      throw new Error(`指定路径不是文件: ${filename}`);
    }

    console.log(`✓ 文件解析成功: ${filename} -> ${fullPath}`);
    return fullPath;
  }

  static resolveMultipleFilePaths(filenames) {
    return filenames.map(filename => this.resolveFilePath(filename));
  }

  static getAvailableFiles() {
    const staticDirPath = path.join(process.cwd(), this.STATIC_DIR);
    
    if (!fs.existsSync(staticDirPath)) {
      return [];
    }

    return fs.readdirSync(staticDirPath)
      .filter(item => {
        const itemPath = path.join(staticDirPath, item);
        return fs.statSync(itemPath).isFile();
      });
  }
}

// 运行测试
console.log('=== FilePathResolver 测试 ===\n');

console.log('1. 检查可用文件:');
const availableFiles = TestFilePathResolver.getAvailableFiles();
console.log('可用文件:', availableFiles);
console.log('');

console.log('2. 测试单文件解析:');
try {
  TestFilePathResolver.resolveFilePath('test.txt');
  TestFilePathResolver.resolveFilePath('sample.csv');
  TestFilePathResolver.resolveFilePath('empty.txt');
} catch (error) {
  console.error('❌ 错误:', error.message);
}
console.log('');

console.log('3. 测试多文件解析:');
try {
  const paths = TestFilePathResolver.resolveMultipleFilePaths(['test.txt', 'sample.csv']);
  console.log('✓ 多文件解析成功:', paths.length, '个文件');
} catch (error) {
  console.error('❌ 错误:', error.message);
}
console.log('');

console.log('4. 测试错误情况:');
try {
  TestFilePathResolver.resolveFilePath('nonexistent.txt');
} catch (error) {
  console.log('✓ 正确捕获错误:', error.message);
}

try {
  TestFilePathResolver.resolveFilePath('path/with/slash.txt');
} catch (error) {
  console.log('✓ 正确捕获路径分隔符错误:', error.message);
}

try {
  TestFilePathResolver.resolveFilePath('');
} catch (error) {
  console.log('✓ 正确捕获空文件名错误:', error.message);
}

console.log('\n=== 测试完成 ===');
