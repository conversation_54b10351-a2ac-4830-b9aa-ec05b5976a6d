config:
  name: "文件上传功能测试"
  description: "测试 setInputFiles action 的文件上传功能"
  baseUrl: "file://"
  testMode: "flow"
  timeout: 30000

tests:
  - name: "单文件上传测试"
    description: "测试上传单个文件"
    steps:
      - action: navigate
        url: "./test-upload.html"

      - action: setInputFiles
        selector: "#singleFile"
        files: "test.txt"

      - action: click
        selector: ".upload-button"

      - action: verify
        type: element
        selector: "#singleFileInfo"
        assertion: "visible"

  - name: "多文件上传测试"
    description: "测试上传多个文件"
    steps:
      - action: navigate
        url: "./test-upload.html"

      - action: setInputFiles
        selector: "#multipleFiles"
        files:
          - "test.txt"
          - "sample.csv"

      - action: click
        selector: ".upload-button"

      - action: verify
        type: element
        selector: "#multipleFilesInfo"
        assertion: "visible"

  - name: "空文件上传测试"
    description: "测试上传空文件"
    steps:
      - action: navigate
        url: "./test-upload.html"

      - action: setInputFiles
        selector: "#singleFile"
        files: "empty.txt"

      - action: click
        selector: ".upload-button"

      - action: verify
        type: element
        selector: "#singleFileInfo"
        assertion: "visible"

  - name: "使用role定位器上传文件"
    description: "测试使用role定位器进行文件上传"
    steps:
      - action: navigate
        url: "./test-upload.html"

      - action: setInputFiles
        role: "button"
        roleOptions:
          name: "上传单个文件"
        files: "test.txt"
