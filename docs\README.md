# 自动化测试框架文档中心

欢迎来到自动化测试框架的文档中心！这里包含了框架的完整文档，从基础概念到高级用法，从架构设计到实战示例。

## 📚 文档导航

### 🚀 快速开始
- **[完整教程](./complete-tutorial.md)** - 从零开始的完整学习指南，包含实战示例和最佳实践

### 🏗️ 架构文档
- **[项目架构概览](./architecture-overview.md)** - 框架整体架构、设计原则和核心模块
- **[系统架构图](./architecture-overview.md#系统架构图)** - 可视化的系统组件关系图

### 🔄 执行流程
- **[执行流程详解](./execution-flow.md)** - 测试执行的完整流程，从YAML解析到结果收集
- **[执行流程图](./execution-flow.md#执行流程图)** - 可视化的执行流程和决策点

### 📋 模板系统
- **[模板系统详解](./template-system.md)** - 模板优先工作流、优先级机制和使用方法
- **[模板处理流程图](./template-system.md#模板处理流程图)** - 模板处理和验证的完整流程

### 💾 数据管理
- **[数据管理系统](./data-management.md)** - 数据源管理、变量解析和API数据获取
- **[数据流图](./data-management.md#数据流图)** - 数据在系统中的流动和转换过程

### ⚙️ 执行器系统
- **[执行器系统详解](./executor-system.md)** - 执行器架构、类型和UI操作处理机制
- **[执行器架构图](./executor-system.md#执行器架构图)** - 执行器系统的类图和交互图

### 🔗 API集成
- **[API调用路径文档](./api-call-paths.md)** - 主要函数调用链和集成点的详细说明

## 📖 阅读建议

### 新手用户
1. 从 **[完整教程](./complete-tutorial.md)** 开始
2. 阅读 **[项目架构概览](./architecture-overview.md)** 了解整体设计
3. 学习 **[模板系统详解](./template-system.md)** 掌握核心概念
4. 参考教程中的实战示例进行练习

### 开发者
1. 阅读 **[架构概览](./architecture-overview.md)** 了解系统设计
2. 查看 **[API调用路径](./api-call-paths.md)** 理解内部机制
3. 学习 **[执行器系统](./executor-system.md)** 进行扩展开发
4. 参考 **[数据管理系统](./data-management.md)** 进行数据处理定制

### 高级用户
1. 深入研究各个系统的详细文档
2. 查看流程图理解系统工作原理
3. 参考API文档进行深度集成
4. 基于架构文档进行系统扩展

## 🎯 核心特性

### ✅ 模板优先工作流
- **强制验证**：所有模板必须先验证后使用
- **渐进式构建**：模板 → 完整YAML → 执行
- **复用优先**：优先使用已验证的模板

### ✅ 智能执行器选择
- **自动选择**：根据上下文和动作自动选择执行器
- **多执行器支持**：Web、Element Plus等不同执行器
- **可扩展**：支持自定义执行器开发

### ✅ 强大的数据管理
- **多数据源**：API、Mock等多种数据源支持
- **变量系统**：统一的变量访问和解析机制
- **类型保持**：变量解析时保持原始数据类型

### ✅ 完整的错误处理
- **分层处理**：每层都有对应的错误处理机制
- **优雅降级**：关键错误时提供默认行为
- **详细信息**：提供具体的错误位置和修复建议

## 🛠️ 技术栈

- **核心框架**：TypeScript + Playwright
- **配置格式**：YAML DSL
- **验证系统**：JSON Schema + AJV
- **模板引擎**：自定义参数化模板系统
- **数据处理**：统一数据访问器
- **执行引擎**：插件化执行器架构

## 📊 架构图表

本文档包含多个Mermaid图表，帮助理解系统架构：

- **系统架构图**：展示各组件之间的关系和依赖
- **执行流程图**：详细的测试执行流程和决策点
- **模板处理流程图**：模板处理和验证的完整流程
- **数据流图**：数据在系统中的流动和转换过程
- **执行器架构图**：执行器系统的类图和交互图

## 🔧 使用场景

### Web应用测试
- 标准Web页面的UI自动化测试
- 表单填写、按钮点击、页面导航
- 数据验证和断言检查

### Element Plus组件测试
- Vue.js + Element Plus应用的专门测试
- 下拉选择、日期选择等组件特化处理
- 复杂表单和数据表格操作

### API集成测试
- 前端与后端API的集成验证
- 数据同步和状态检查
- 性能监控和指标收集

### 数据驱动测试
- 基于API数据源的批量测试
- 多用户、多场景的自动化验证
- 测试数据的动态生成和管理

## 🚀 快速链接

- **[开始使用](./complete-tutorial.md#快速开始)** - 环境准备和第一个测试
- **[核心概念](./complete-tutorial.md#核心概念)** - 理解框架的基本概念
- **[实战示例](./complete-tutorial.md#实战示例)** - 完整的电商测试场景
- **[最佳实践](./complete-tutorial.md#最佳实践)** - 测试设计和开发建议
- **[故障排除](./complete-tutorial.md#故障排除)** - 常见问题和解决方案

## 📝 文档更新

本文档集合会随着框架的发展持续更新，确保内容与实际实现保持同步。如果您发现任何问题或有改进建议，欢迎提出反馈。

---

**开始您的自动化测试之旅吧！** 🎉

从 **[完整教程](./complete-tutorial.md)** 开始，逐步掌握这个强大的自动化测试框架。
