# 自动化测试框架完整教程

## 目录

1. [框架概述](#框架概述)
2. [快速开始](#快速开始)
3. [核心概念](#核心概念)
4. [架构深入](#架构深入)
5. [使用指南](#使用指南)
6. [最佳实践](#最佳实践)
7. [故障排除](#故障排除)
8. [扩展开发](#扩展开发)

## 框架概述

### 什么是自动化测试框架

这是一个基于 Playwright 的现代化浏览器自动化测试框架，采用 TypeScript 开发，专为复杂的Web应用测试场景设计。框架提供了：

- **模板化测试**：可复用的测试组件
- **数据驱动测试**：支持API和Mock数据源
- **多执行器支持**：Web和Element Plus组件特化
- **智能验证**：YAML语法和业务逻辑验证
- **AI辅助**：LLM优化的测试生成

### 核心特性

- ✅ **模板优先工作流**：强制验证，确保测试可靠性
- ✅ **智能执行器选择**：自动选择最适合的执行器
- ✅ **强大的数据管理**：统一的变量系统和数据源支持
- ✅ **完整的错误处理**：分层错误处理和恢复机制
- ✅ **高性能设计**：并发处理和缓存优化
- ✅ **可扩展架构**：插件化设计，支持自定义扩展

## 快速开始

### 环境准备

1. **安装依赖**
```bash
npm install
```

2. **配置环境变量**
```bash
# 创建 .env 文件
cp .env.example .env

# 编辑配置
API_BASE_URL=https://your-api.com
BROWSER_DEBUGGING_PORT=9222
```

3. **启动浏览器调试模式**
```bash
# Chrome调试模式
chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome-debug
```

### 第一个测试

1. **创建测试文件** `my-first-test.yml`
```yaml
config:
  name: "我的第一个测试"
  baseUrl: "https://example.com"
  executorType: "web"

tests:
  - name: "简单点击测试"
    steps:
      - action: navigate
        url: "/"
      - action: click
        role: button
        roleOptions:
          name: "登录"
      - action: verify
        type: "page"
        assertion: "url"
        expected: "/login"
```

2. **运行测试**
```typescript
import { TestingEngine } from './src/testing-engine';

const engine = new TestingEngine({
  connectionType: 'cdp',
  cdpEndpoint: 'http://localhost:9222'
});

await engine.runYamlFile('my-first-test.yml');
```

## 核心概念

### 1. 模板优先工作流

框架采用严格的模板优先工作流，确保测试的可靠性：

```mermaid
graph LR
    A[模板验证] --> B[完整YAML验证] --> C[测试执行]
```

**阶段说明**：
- **模板验证**：使用 `executeTemplateTest()` 验证模板功能
- **完整YAML验证**：使用 `validateYaml()` 验证语法和结构
- **测试执行**：使用 `executeYamlTest()` 执行完整测试

### 2. 执行器系统

框架支持多种执行器，自动选择最适合的执行器：

- **WebExecutor**：标准Web操作（默认）
- **ElementPlusExecutor**：Element Plus组件特化
- **CustomExecutor**：自定义执行器（可扩展）

### 3. 数据管理

统一的数据访问接口，支持多种变量类型：

```yaml
# 普通变量
data: "{{userName}}"

# 配置变量（只读）
url: "{{config.baseUrl}}/api"

# 嵌套属性
email: "{{user.profile.email}}"
```

### 4. 模板系统

支持内联模板和全局模板，按优先级解析：

```yaml
# 内联模板（高优先级）
templates:
  login-template:
    parameters:
      - name: username
        type: string
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "用户名"
        data: "{{username}}"

# 使用模板
tests:
  - name: "登录测试"
    steps:
      - action: useTemplate
        template: "login-template"
        parameters:
          username: "testuser"
```

## 架构深入

### 系统架构

框架采用分层架构设计：

```
表示层：TestingEngine, APIService
业务逻辑层：TestService, ConfigService, ExecutionEngine
基础设施层：ExecutorService, TemplateSystem, DataManagement
数据访问层：DSLParser, YAMLValidator, DataAccessor
```

详细架构图请参考：[系统架构图](./architecture-overview.md)

### 执行流程

完整的测试执行流程包括：

1. **初始化阶段**：加载配置，初始化服务
2. **验证阶段**：YAML解析和验证
3. **数据准备**：数据源获取和变量初始化
4. **执行阶段**：测试用例和步骤执行
5. **清理阶段**：资源释放和结果收集

详细流程图请参考：[执行流程图](./execution-flow.md)

### 模板处理

模板系统的完整处理流程：

1. **模板注册**：扫描和注册全局模板
2. **模板解析**：按优先级查找模板
3. **参数验证**：验证模板参数
4. **模板执行**：解析参数并执行步骤

详细流程请参考：[模板系统](./template-system.md)

## 使用指南

### 基础用法

#### 1. 创建TestingEngine实例
```typescript
const testingEngine = new TestingEngine({
  connectionType: 'cdp',
  headless: false,
  debuggingPort: 9222,
  cdpEndpoint: 'http://localhost:9222',
  timeout: 30000,
  outputDir: './test-results',
  captureScreenshots: false,
  continueOnFailure: false
});
```

#### 2. 执行YAML测试
```typescript
// 执行文件
const result = await testingEngine.runYamlFile('test.yml');

// 执行内容
const yamlContent = `
config:
  name: "测试"
tests:
  - name: "示例测试"
    steps:
      - action: navigate
        url: "https://example.com"
`;
const result = await testingEngine.runYamlContent(yamlContent);
```

#### 3. 执行单个步骤
```typescript
const stepResult = await testingEngine.executeStep({
  action: 'click',
  role: 'button',
  roleOptions: { name: '提交' }
});
```

### 高级用法

#### 1. 数据驱动测试
```yaml
config:
  name: "数据驱动测试"
  dataSources:
    - name: "testData"
      type: "api"
      config:
        url: "{{config.apiBaseUrl}}/test-data"
        method: "GET"

tests:
  - name: "批量测试"
    steps:
      - action: forEach
        data: "{{testData}}"
        steps:
          - action: fill
            role: textbox
            roleOptions:
              name: "输入框"
            data: "{{currentItem.value}}"
```

#### 2. 模板化测试
```yaml
templates:
  form-fill-template:
    name: "表单填写模板"
    parameters:
      - name: formData
        type: object
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "姓名"
        data: "{{formData.name}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "邮箱"
        data: "{{formData.email}}"

tests:
  - name: "用户注册测试"
    steps:
      - action: useTemplate
        template: "form-fill-template"
        parameters:
          formData:
            name: "张三"
            email: "<EMAIL>"
```

#### 3. 条件执行和验证
```yaml
tests:
  - name: "条件测试"
    steps:
      - action: click
        role: button
        roleOptions:
          name: "提交"
      - action: verify
        type: "element"
        role: "alert"
        assertion: "visible"
        timeout: 5000
      - action: verify
        type: "text"
        role: "alert"
        assertion: "contains"
        expected: "提交成功"
```

## 最佳实践

### 1. 测试设计原则

#### YAML结构设计
```yaml
# ✅ 推荐：清晰的结构和命名
config:
  name: "用户管理模块测试"
  description: "测试用户的增删改查功能"
  baseUrl: "https://app.example.com"
  testMode: "flow"  # flow | boundary

# ✅ 推荐：使用模板提高复用性
templates:
  user-form-template:
    name: "用户表单模板"
    parameters:
      - name: userData
        type: object
        required: true
    steps:
      # 模板步骤定义

tests:
  - name: "用户创建测试"
    description: "验证用户创建功能的完整流程"
    steps:
      - action: useTemplate
        template: "user-form-template"
        parameters:
          userData: "{{testData.newUser}}"
```

#### 元素定位最佳实践
```yaml
# ✅ 推荐：使用role定位（更稳定）
- action: click
  role: button
  roleOptions:
    name: "保存"

# ✅ 推荐：使用语义化的role选项
- action: fill
  role: textbox
  roleOptions:
    name: "用户名"
    placeholder: "请输入用户名"

# ❌ 避免：过度依赖CSS选择器
- action: click
  selector: "#submit-btn-123"  # 脆弱的选择器
```

### 2. 模板设计原则

#### 模板参数化
```yaml
# ✅ 推荐：充分参数化
templates:
  search-template:
    parameters:
      - name: searchCriteria
        type: object
        required: true
      - name: expectedResults
        type: number
        default: 1
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "搜索框"
        data: "{{searchCriteria.keyword}}"
      - action: selectOption
        role: combobox
        roleOptions:
          name: "分类"
        data: "{{searchCriteria.category}}"
      - action: verify
        type: "count"
        role: "listitem"
        assertion: "greaterThanOrEqual"
        expected: "{{expectedResults}}"
```

#### 模板组织结构
```
templates/
├── universal/          # 通用模板
│   ├── auth.yml       # 认证相关
│   ├── navigation.yml # 导航相关
│   └── forms.yml      # 表单相关
└── business/          # 业务模板
    ├── user-mgmt.yml  # 用户管理
    ├── order-mgmt.yml # 订单管理
    └── report.yml     # 报表相关
```

### 3. 数据管理最佳实践

#### 数据源配置
```yaml
config:
  dataSources:
    # ✅ 推荐：明确的数据源命名
    - name: "userTestData"
      type: "api"
      config:
        url: "{{config.apiBaseUrl}}/test-data/users"
        method: "GET"
        headers:
          Authorization: "Bearer {{config.apiToken}}"
    
    # ✅ 推荐：Mock数据用于稳定测试
    - name: "mockCategories"
      type: "mock"
      config:
        data:
          - { id: 1, name: "电子产品" }
          - { id: 2, name: "服装鞋帽" }
```

#### 变量命名规范
```yaml
# ✅ 推荐：清晰的变量命名
variables:
  currentUser: "{{userTestData.0}}"
  searchKeyword: "{{testScenario.keyword}}"
  expectedResultCount: 5

# ✅ 推荐：使用配置变量
steps:
  - action: navigate
    url: "{{config.baseUrl}}/users"
  - action: verify
    type: "page"
    assertion: "url"
    expected: "{{config.baseUrl}}/users"
```

### 4. 错误处理最佳实践

#### 优雅的错误处理
```yaml
config:
  continueOnFailure: true  # 继续执行后续步骤
  captureScreenshots: true # 失败时截图

tests:
  - name: "容错测试"
    steps:
      - action: click
        role: button
        roleOptions:
          name: "可能不存在的按钮"
        options:
          timeout: 5000  # 设置合理的超时
      - action: verify
        type: "element"
        role: "alert"
        assertion: "visible"
        timeout: 3000
        # 验证失败不会中断整个测试
```

#### 重试机制配置
```yaml
config:
  retries: 2  # 全局重试次数

tests:
  - name: "网络敏感测试"
    config:
      retries: 3  # 测试级别重试
    steps:
      - action: navigate
        url: "{{config.baseUrl}}/slow-page"
        options:
          timeout: 10000  # 增加超时时间
```

## 故障排除

### 常见问题

#### 1. 浏览器连接问题
```bash
# 问题：无法连接到浏览器
# 解决：检查Chrome调试端口
chrome --remote-debugging-port=9222 --user-data-dir=/tmp/chrome-debug

# 验证连接
curl http://localhost:9222/json/version
```

#### 2. 元素定位失败
```yaml
# 问题：元素未找到
# 解决：添加等待条件
- action: waitForSelector
  selector: "[data-testid='submit-button']"
  options:
    timeout: 10000
- action: click
  role: button
  roleOptions:
    name: "提交"
```

#### 3. 模板验证失败
```typescript
// 问题：模板参数不匹配
// 解决：检查参数定义和传入值
const templateYaml = `
templates:
  test-template:
    parameters:
      - name: requiredParam
        type: string
        required: true  # 确保必需参数
`;

const parameters = {
  "test-template": {
    requiredParam: "value"  // 确保提供必需参数
  }
};
```

### 调试技巧

#### 1. 启用详细日志
```typescript
const testingEngine = new TestingEngine({
  // 其他配置...
  verbose: true,  // 启用详细日志
  captureScreenshots: true  // 启用截图
});
```

#### 2. 使用页面API调试
```javascript
// 在浏览器控制台中使用
await window.automationTesting.getSnapshotForAI();
await window.automationTesting.validateYaml(yamlContent);
```

#### 3. 分步调试
```yaml
# 将复杂测试分解为简单步骤
tests:
  - name: "调试测试 - 第1步"
    steps:
      - action: navigate
        url: "/"
  - name: "调试测试 - 第2步"
    steps:
      - action: click
        role: button
        roleOptions:
          name: "登录"
```

## 扩展开发

### 自定义执行器

```typescript
import { WebExecutor } from './src/execution/executors/web-executor';

class MyCustomExecutor extends WebExecutor {
  protected async executeCustomAction(step: TestStep, page: Page): Promise<void> {
    // 实现自定义操作
    console.log('执行自定义操作:', step.action);
  }
}

// 注册自定义执行器
executorService.registerExecutor({
  type: 'my-custom',
  executorClass: MyCustomExecutor,
  description: '我的自定义执行器'
});
```

### 自定义数据源

```typescript
import { DataSourceManager } from './src/data/data-source-manager';

class CustomDataSourceManager extends DataSourceManager {
  protected async fetchFromCustomSource(
    dataSource: DataSourceConfig,
    variables: Record<string, any>
  ): Promise<any> {
    // 实现自定义数据源逻辑
    return customData;
  }
}
```

### 自定义验证器

```typescript
import { YAMLSyntaxValidator } from './src/validation/yaml-validator';

class CustomValidator extends YAMLSyntaxValidator {
  protected validateCustomRules(parsed: any): ValidationError[] {
    const errors: ValidationError[] = [];
    // 实现自定义验证规则
    return errors;
  }
}
```

---

## 实战示例

### 完整的电商测试场景

以下是一个完整的电商网站测试示例，展示了框架的各种功能：

```yaml
config:
  name: "电商网站完整测试"
  description: "包含用户注册、登录、商品搜索、购物车、订单流程的完整测试"
  baseUrl: "https://shop.example.com"
  testMode: "flow"
  executorType: "element-plus"
  captureScreenshots: true
  continueOnFailure: false

  # 数据源配置
  dataSources:
    - name: "testUsers"
      type: "api"
      config:
        url: "{{config.apiBaseUrl}}/test-data/users"
        method: "GET"
        headers:
          Authorization: "Bearer {{config.apiToken}}"

    - name: "productCategories"
      type: "mock"
      config:
        data:
          - { id: 1, name: "电子产品", keywords: ["手机", "电脑", "平板"] }
          - { id: 2, name: "服装鞋帽", keywords: ["T恤", "牛仔裤", "运动鞋"] }
          - { id: 3, name: "家居用品", keywords: ["沙发", "床垫", "台灯"] }

  # 全局变量
  variables:
    testUser: "{{testUsers.0}}"
    searchCategory: "{{productCategories.0}}"
    orderTimeout: 30000

# 内联模板定义
templates:
  # 用户注册模板
  user-registration-template:
    name: "用户注册模板"
    description: "处理用户注册流程的通用模板"
    parameters:
      - name: userInfo
        type: object
        required: true
        description: "用户信息对象"
      - name: skipEmailVerification
        type: boolean
        default: false
        description: "是否跳过邮箱验证"
    steps:
      - action: navigate
        url: "/register"
      - action: fill
        role: textbox
        roleOptions:
          name: "用户名"
        data: "{{userInfo.username}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "邮箱"
        data: "{{userInfo.email}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "密码"
        data: "{{userInfo.password}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "确认密码"
        data: "{{userInfo.password}}"
      - action: check
        role: checkbox
        roleOptions:
          name: "同意用户协议"
      - action: click
        role: button
        roleOptions:
          name: "注册"
      - action: verify
        type: "element"
        role: "alert"
        assertion: "visible"
        timeout: 5000
      - action: verify
        type: "text"
        role: "alert"
        assertion: "contains"
        expected: "注册成功"

  # 商品搜索模板
  product-search-template:
    name: "商品搜索模板"
    parameters:
      - name: searchKeyword
        type: string
        required: true
      - name: category
        type: string
        required: false
      - name: minResults
        type: number
        default: 1
    steps:
      - action: fill
        role: searchbox
        roleOptions:
          name: "商品搜索"
        data: "{{searchKeyword}}"
      - action: selectOption
        role: combobox
        roleOptions:
          name: "商品分类"
        data: "{{category}}"
        condition: "{{category}}"  # 只有提供分类时才执行
      - action: click
        role: button
        roleOptions:
          name: "搜索"
      - action: waitForSelector
        selector: "[data-testid='search-results']"
        options:
          timeout: 10000
      - action: verify
        type: "count"
        role: "article"
        roleOptions:
          name: /商品卡片/
        assertion: "greaterThanOrEqual"
        expected: "{{minResults}}"

  # 购物车操作模板
  shopping-cart-template:
    name: "购物车操作模板"
    parameters:
      - name: action
        type: string
        required: true
        description: "操作类型：add, remove, update"
      - name: productIndex
        type: number
        default: 0
      - name: quantity
        type: number
        default: 1
    steps:
      - action: click
        role: "article"
        roleOptions:
          name: /商品卡片/
        data: "{{productIndex}}"  # 选择第N个商品
      - action: fill
        role: spinbutton
        roleOptions:
          name: "数量"
        data: "{{quantity}}"
        condition: "{{action === 'add' || action === 'update'}}"
      - action: click
        role: button
        roleOptions:
          name: "加入购物车"
        condition: "{{action === 'add'}}"
      - action: click
        role: button
        roleOptions:
          name: "移除"
        condition: "{{action === 'remove'}}"
      - action: verify
        type: "element"
        role: "status"
        assertion: "visible"
        timeout: 3000

# 测试用例
tests:
  - name: "用户注册流程测试"
    description: "测试新用户注册的完整流程"
    tags: ["registration", "user-management"]
    steps:
      - action: useTemplate
        template: "user-registration-template"
        parameters:
          userInfo:
            username: "{{testUser.username}}"
            email: "{{testUser.email}}"
            password: "{{testUser.password}}"
          skipEmailVerification: true

  - name: "商品搜索功能测试"
    description: "测试商品搜索和筛选功能"
    tags: ["search", "product"]
    steps:
      - action: navigate
        url: "/"
      - action: useTemplate
        template: "product-search-template"
        parameters:
          searchKeyword: "{{searchCategory.keywords.0}}"
          category: "{{searchCategory.name}}"
          minResults: 3

  - name: "购物车完整流程测试"
    description: "测试添加商品到购物车、修改数量、结算的完整流程"
    tags: ["cart", "order"]
    steps:
      # 先搜索商品
      - action: useTemplate
        template: "product-search-template"
        parameters:
          searchKeyword: "{{searchCategory.keywords.1}}"
          minResults: 1

      # 添加商品到购物车
      - action: useTemplate
        template: "shopping-cart-template"
        parameters:
          action: "add"
          productIndex: 0
          quantity: 2

      # 查看购物车
      - action: click
        role: link
        roleOptions:
          name: "购物车"

      # 验证商品已添加
      - action: verify
        type: "count"
        role: "row"
        roleOptions:
          name: /购物车商品/
        assertion: "greaterThanOrEqual"
        expected: 1

      # 修改商品数量
      - action: fill
        role: spinbutton
        roleOptions:
          name: "数量"
        data: "3"

      # 结算
      - action: click
        role: button
        roleOptions:
          name: "去结算"

      # 验证进入结算页面
      - action: verify
        type: "page"
        assertion: "url"
        expected: "/checkout"

  - name: "数据驱动的多用户测试"
    description: "使用数据源中的多个用户进行批量测试"
    tags: ["data-driven", "batch"]
    steps:
      - action: forEach
        data: "{{testUsers}}"
        steps:
          - action: useTemplate
            template: "user-registration-template"
            parameters:
              userInfo:
                username: "{{currentItem.username}}"
                email: "{{currentItem.email}}"
                password: "{{currentItem.password}}"

          # 注册后立即登录验证
          - action: navigate
            url: "/login"
          - action: fill
            role: textbox
            roleOptions:
              name: "用户名"
            data: "{{currentItem.username}}"
          - action: fill
            role: textbox
            roleOptions:
              name: "密码"
            data: "{{currentItem.password}}"
          - action: click
            role: button
            roleOptions:
              name: "登录"
          - action: verify
            type: "page"
            assertion: "url"
            expected: "/dashboard"

# 钩子函数
hooks:
  beforeAll:
    - action: executeScript
      script: |
        // 清理浏览器缓存和Cookie
        await page.evaluate(() => {
          localStorage.clear();
          sessionStorage.clear();
        });
        console.log('测试环境已清理');

  beforeEach:
    - action: navigate
      url: "/"
    - action: wait
      duration: 1000  # 等待页面稳定

  afterEach:
    - action: executeScript
      script: |
        // 记录测试执行信息
        const testInfo = {
          url: window.location.href,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent
        };
        console.log('测试步骤完成:', testInfo);

  afterAll:
    - action: executeScript
      script: |
        console.log('所有测试完成，开始清理');
        // 执行最终清理工作
```

### API集成测试示例

```yaml
config:
  name: "API集成测试"
  description: "测试前端与后端API的集成"
  baseUrl: "https://app.example.com"

  dataSources:
    - name: "apiTestData"
      type: "api"
      config:
        url: "{{config.apiBaseUrl}}/test-setup"
        method: "POST"
        body:
          testSuite: "frontend-integration"
          environment: "staging"
        headers:
          Authorization: "Bearer {{config.apiToken}}"

tests:
  - name: "API数据同步测试"
    description: "验证前端页面与API数据的同步"
    steps:
      - action: navigate
        url: "/api-test-page"

      # 触发API调用
      - action: click
        role: button
        roleOptions:
          name: "刷新数据"

      # 等待API响应
      - action: waitForSelector
        selector: "[data-testid='api-loading']"
        options:
          state: "hidden"
          timeout: 10000

      # 验证API数据显示
      - action: verify
        type: "text"
        role: "cell"
        roleOptions:
          name: /数据行/
        assertion: "contains"
        expected: "{{apiTestData.expectedValue}}"

      # 验证数据数量
      - action: verify
        type: "count"
        role: "row"
        assertion: "equals"
        expected: "{{apiTestData.expectedCount}}"
```

## 性能测试指南

### 性能监控配置

```yaml
config:
  name: "性能测试"
  captureScreenshots: false  # 关闭截图提高性能

  # 性能监控配置
  performance:
    enabled: true
    metrics:
      - "loadTime"
      - "domContentLoaded"
      - "firstContentfulPaint"
      - "largestContentfulPaint"

tests:
  - name: "页面加载性能测试"
    steps:
      - action: navigate
        url: "/"
        options:
          waitUntil: "networkidle"  # 等待网络空闲

      - action: executeScript
        script: |
          // 获取性能指标
          const perfData = performance.getEntriesByType('navigation')[0];
          const metrics = {
            loadTime: perfData.loadEventEnd - perfData.loadEventStart,
            domContentLoaded: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
            ttfb: perfData.responseStart - perfData.requestStart
          };

          // 验证性能指标
          if (metrics.loadTime > 3000) {
            throw new Error(`页面加载时间过长: ${metrics.loadTime}ms`);
          }

          return metrics;
        variable: "performanceMetrics"

      - action: verify
        type: "script"
        script: "return {{performanceMetrics.loadTime}} < 3000"
        assertion: "true"
```

## 总结

本教程涵盖了自动化测试框架的核心概念、使用方法和最佳实践。通过遵循模板优先工作流和最佳实践，您可以构建可靠、可维护的自动化测试。

### 关键要点

1. **模板优先**：始终先验证模板，再构建完整测试
2. **数据驱动**：充分利用数据源和变量系统
3. **智能选择**：让框架自动选择最适合的执行器
4. **错误处理**：设计容错性强的测试用例
5. **性能考虑**：在测试设计中考虑性能影响

### 下一步

1. 阅读详细的[架构文档](./architecture-overview.md)
2. 查看[API调用路径](./api-call-paths.md)了解内部机制
3. 参考[模板系统](./template-system.md)设计可复用组件
4. 学习[执行器系统](./executor-system.md)进行深度定制
5. 查看[数据管理系统](./data-management.md)了解数据处理

### 获取帮助

- 查看项目README文档
- 参考示例测试文件
- 使用框架内置的调试工具
- 启用详细日志进行问题诊断
- 查看[执行流程文档](./execution-flow.md)了解详细执行过程

### 相关资源

- [系统架构图](./architecture-overview.md#系统架构图)
- [执行流程图](./execution-flow.md#执行流程图)
- [模板处理流程图](./template-system.md#模板处理流程图)
- [数据流图](./data-management.md#数据流图)
- [执行器架构图](./executor-system.md#执行器架构图)

*祝您使用愉快！*
