# 数据管理系统详解

## 概述

数据管理系统是自动化测试框架的核心基础设施，负责处理测试数据的获取、存储、解析和访问。系统采用统一的数据访问接口，支持多种数据源类型，并提供强大的变量解析和参数替换功能。

## 核心设计原则

### 1. 统一数据访问
- **单一接口**：通过 DataAccessor 提供统一的数据访问方式
- **命名空间隔离**：支持普通变量和配置变量的命名空间分离
- **类型保持**：变量解析时保持原始数据类型

### 2. 预加载机制
- **数据预获取**：所有数据源在测试执行前完成加载
- **并行处理**：多个数据源并行获取，提高效率
- **错误快速失败**：数据获取失败时立即终止，避免无效测试

### 3. 灵活的变量系统
- **多层级变量**：支持全局、测试、步骤级别的变量作用域
- **动态解析**：支持运行时变量替换和表达式计算
- **嵌套访问**：支持对象属性的深度访问

## 核心组件架构

### 1. DataAccessor（数据访问器）
**职责**：统一的数据访问接口，支持变量解析和配置访问

```typescript
class DataAccessor {
  // 获取变量值，支持命名空间
  get(key: string): any;
  
  // 设置变量值
  set(key: string, value: any): void;
  
  // 解析字符串中的变量引用
  resolve(expression: string): any;
  
  // 解析对象中的所有变量引用
  resolveObject(obj: any, templateParameters?: Record<string, any>): any;
}
```

**支持的命名空间**：
- **普通变量**：`{{variableName}}` - 用户定义的测试变量
- **配置变量**：`{{config.propertyName}}` - 系统配置变量（只读）
- **嵌套属性**：`{{user.profile.name}}` - 支持对象属性访问

### 2. DataSourceManager（数据源管理器）
**职责**：管理多种数据源的数据获取和处理

```typescript
interface DataSourceConfig {
  name: string;                       // 响应数据对应的变量名
  type: 'api' | 'mock';              // 数据源类型
  config: {
    url?: string;                     // API数据源的URL
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
    data?: any;                       // mock数据源的数据
  };
}
```

**支持的数据源类型**：
- **API数据源**：从REST API获取数据
- **Mock数据源**：使用配置中的模拟数据

## 数据源配置与使用

### 1. API数据源配置
```yaml
config:
  name: "用户管理测试"
  dataSources:
    - name: "userData"              # 数据将存储在 userData 变量中
      type: "api"
      config:
        url: "{{config.apiBaseUrl}}/users"
        method: "GET"
        headers:
          Authorization: "Bearer {{config.apiToken}}"
        timeout: 30000
    - name: "formData"
      type: "api"
      config:
        url: "{{config.apiBaseUrl}}/forms/{{testMode}}"
        method: "POST"
        body:
          testMode: "{{config.testMode}}"
        timeout: 15000
```

### 2. Mock数据源配置
```yaml
config:
  dataSources:
    - name: "mockUsers"
      type: "mock"
      config:
        data:
          - id: 1
            name: "张三"
            email: "<EMAIL>"
          - id: 2
            name: "李四"
            email: "<EMAIL>"
```

### 3. 数据源使用示例
```yaml
tests:
  - name: "用户数据测试"
    steps:
      # 使用API数据源获取的数据
      - action: fill
        role: textbox
        roleOptions:
          name: "用户名"
        data: "{{userData.0.name}}"
      
      # 使用Mock数据源的数据
      - action: forEach
        data: "{{mockUsers}}"
        steps:
          - action: fill
            role: textbox
            roleOptions:
              name: "邮箱"
            data: "{{currentItem.email}}"
```

## 变量解析机制

### 1. 变量解析优先级
1. **模板参数**（最高优先级）- 模板调用时传入的参数
2. **用户定义变量**（中优先级）- 通过 set() 方法设置的变量
3. **配置变量**（最低优先级）- 系统配置中的变量

### 2. 变量解析语法
```typescript
// 纯变量引用（保持原始类型）
"{{userName}}"          // 返回原始值，如字符串、数字、对象等
"{{config.timeout}}"    // 返回配置中的超时值
"{{userData.0.id}}"     // 返回数组第一个元素的id属性

// 字符串模板（转换为字符串）
"用户名：{{userName}}"   // 字符串拼接
"API地址：{{config.baseUrl}}/api/users"  // URL拼接
```

### 3. 嵌套属性访问
```typescript
// 支持深度嵌套访问
const data = {
  user: {
    profile: {
      personal: {
        name: "张三",
        age: 30
      }
    }
  }
};

// 访问方式
"{{user.profile.personal.name}}"  // 返回 "张三"
"{{user.profile.personal.age}}"   // 返回 30
```

## 数据流处理流程

### 1. 数据源初始化流程
```
解析配置 → 验证数据源配置 → 并行获取数据 → 变量解析 → 存储到DataAccessor
```

### 2. 变量解析流程
```
接收表达式 → 识别变量类型 → 按优先级查找 → 执行嵌套访问 → 返回解析结果
```

### 3. 数据访问流程
```
接收访问请求 → 检查命名空间 → 查找变量值 → 类型转换 → 返回结果
```

## API数据获取详解

### 1. HTTP请求处理
```typescript
// 支持的HTTP方法
const methods = ['GET', 'POST', 'PUT', 'DELETE'];

// 请求配置
const requestConfig = {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer token123'
  },
  body: JSON.stringify(requestData),
  signal: abortController.signal  // 支持超时控制
};
```

### 2. 变量替换处理
```typescript
// URL中的变量替换
const url = "{{config.apiBaseUrl}}/users/{{userId}}/profile";
// 解析后: "https://api.example.com/users/123/profile"

// 请求体中的变量替换
const body = {
  testMode: "{{config.testMode}}",
  userId: "{{currentUser.id}}"
};
```

### 3. 错误处理机制
```typescript
// 超时处理
const controller = new AbortController();
const timeoutId = setTimeout(() => controller.abort(), timeout);

// HTTP错误处理
if (!response.ok) {
  throw new Error(`HTTP ${response.status}: ${response.statusText}`);
}

// 网络错误处理
catch (error) {
  if (error.name === 'AbortError') {
    throw new Error(`请求超时 (${timeout}ms)`);
  }
  throw error;
}
```

## 配置变量系统

### 1. 配置变量访问
```typescript
// 配置变量使用 config. 前缀
"{{config.baseUrl}}"        // 基础URL配置
"{{config.timeout}}"        // 超时配置
"{{config.executorType}}"   // 执行器类型配置
"{{config.testMode}}"       // 测试模式配置
```

### 2. 配置变量特性
- **只读性**：配置变量不能通过 set() 方法修改
- **全局性**：在整个测试执行过程中保持一致
- **层次性**：支持嵌套配置结构的访问

### 3. 可用配置键获取
```typescript
// 获取所有可用的配置键
const availableKeys = dataAccessor.getAvailableConfigKeys();
// 返回: ['config.baseUrl', 'config.timeout', 'config.executorType', ...]
```

## 数据类型处理

### 1. 类型保持机制
```typescript
// 纯变量引用保持原始类型
const numberValue = dataAccessor.resolve("{{count}}");      // 返回 number
const objectValue = dataAccessor.resolve("{{userInfo}}");   // 返回 object
const arrayValue = dataAccessor.resolve("{{userList}}");    // 返回 array

// 字符串模板转换为字符串
const stringValue = dataAccessor.resolve("用户数量：{{count}}"); // 返回 string
```

### 2. 类型转换规则
```typescript
// 值到字符串的转换
private valueToString(value: any): string {
  if (value === null || value === undefined) {
    return '';
  }
  if (typeof value === 'object') {
    return JSON.stringify(value);
  }
  return String(value);
}
```

## 性能优化策略

### 1. 并行数据获取
```typescript
// 多个数据源并行请求
const promises = dataSources.map(ds => this.fetchData(ds, variables));
const responses = await Promise.all(promises);
```

### 2. 缓存机制
- **解析结果缓存**：避免重复解析相同的变量表达式
- **数据源缓存**：相同配置的数据源复用结果
- **配置缓存**：避免重复解析配置结构

### 3. 内存管理
- **及时清理**：测试完成后清理变量数据
- **弱引用**：避免大对象的循环引用
- **分批处理**：大量数据分批加载和处理

## 错误处理与调试

### 1. 数据源错误
```typescript
{
  type: 'DATA_SOURCE',
  severity: 'ERROR',
  message: 'API数据源获取失败: HTTP 404',
  dataSource: 'userData',
  suggestions: [
    '检查API端点是否正确',
    '确认API服务是否可用',
    '验证认证信息是否有效'
  ]
}
```

### 2. 变量解析错误
```typescript
{
  type: 'VARIABLE_RESOLUTION',
  severity: 'WARNING',
  message: '变量 user.profile.name 未找到',
  expression: '{{user.profile.name}}',
  suggestions: [
    '检查变量名是否正确',
    '确认数据源是否已正确加载',
    '验证对象结构是否匹配'
  ]
}
```

### 3. 调试支持
```typescript
// 获取所有变量（调试用）
const allVariables = dataAccessor.getAllVariables();

// 获取所有配置（调试用）
const allConfig = dataAccessor.getAllConfig();

// 获取可用配置键
const configKeys = dataAccessor.getAvailableConfigKeys();
```

---

*数据管理系统为测试框架提供了强大而灵活的数据处理能力，支持复杂的测试场景和数据驱动测试需求。*
