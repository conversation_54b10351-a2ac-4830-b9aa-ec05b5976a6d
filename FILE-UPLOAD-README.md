# 文件上传功能使用说明

## 概述

本项目已实现了简化版的 `setInputFiles` action，支持从统一的静态文件目录上传文件。

## 功能特性

- ✅ 支持单文件上传
- ✅ 支持多文件上传  
- ✅ 统一的静态文件管理
- ✅ 文件存在性验证
- ✅ 详细的错误提示
- ✅ 支持 role 和 selector 两种元素定位方式

## 文件存放位置

所有测试文件需要放置在项目根目录的 `.automation/static/` 文件夹中：

```
project-root/
├── .automation/
│   └── static/
│       ├── test.txt
│       ├── sample.csv
│       ├── empty.txt
│       └── ... (其他测试文件)
```

## 使用方法

### 1. 单文件上传

```yaml
- action: setInputFiles
  selector: "#fileInput"
  files: "test.txt"
```

或使用 role 定位：

```yaml
- action: setInputFiles
  role: button
  roleOptions:
    name: "Upload File"
  files: "test.txt"
```

### 2. 多文件上传

```yaml
- action: setInputFiles
  selector: "#fileInput"
  files: 
    - "test.txt"
    - "sample.csv"
    - "empty.txt"
```

### 3. 使用变量

```yaml
- action: setInputFiles
  selector: "#fileInput"
  data: "{{fileName}}"  # 从变量获取文件名
```

## 错误处理

系统会自动处理以下错误情况：

1. **文件不存在**：如果指定的文件在 `.automation/static/` 目录中不存在
2. **路径安全检查**：文件名不能包含路径分隔符（`/` 或 `\`）
3. **空文件名**：文件名不能为空
4. **非文件对象**：指定路径必须是文件而非目录

## 测试示例

项目中包含了完整的测试示例：

- `test-setinputfiles.yml` - YAML 测试配置
- `test-upload.html` - HTML 测试页面
- `test-file-resolver.js` - 功能验证脚本

## 当前可用的测试文件

- `test.txt` - 包含示例文本的测试文件
- `sample.csv` - CSV 格式的测试数据
- `empty.txt` - 空文件（用于边界测试）

## 技术实现

### 核心组件

1. **FilePathResolver** (`src/utils/file-path-resolver.ts`)
   - 文件路径解析和验证
   - 安全检查和错误处理

2. **WebExecutor.executeSetInputFiles** (`src/execution/executors/web-executor.ts`)
   - 集成文件路径解析
   - 支持单文件和多文件上传

3. **类型定义** (`src/types/index.ts`)
   - 更新了 `files` 字段的注释说明

4. **Schema 定义** (`src/schema/yaml-schema-exporter.ts`)
   - 更新了 YAML schema 和示例

### 工作流程

1. 用户在 YAML 中指定文件名
2. `FilePathResolver` 将文件名解析为完整路径
3. 验证文件是否存在于 `.automation/static/` 目录
4. 调用 Playwright 的 `setInputFiles` 方法上传文件

## 后续扩展

未来可以考虑添加以下功能：

- 动态文件生成（`generateFile` action）
- 文件大小限制测试
- 文件类型验证
- 文件别名映射
- 子目录支持

## 使用建议

1. **文件命名**：使用有意义的文件名，便于测试用例理解
2. **文件大小**：保持测试文件较小，避免影响测试性能
3. **文件类型**：准备不同类型的测试文件以覆盖各种场景
4. **错误测试**：利用不存在的文件名测试错误处理逻辑
