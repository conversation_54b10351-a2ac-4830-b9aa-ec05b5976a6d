import type { Page } from '@playwright/test';

// 执行器类型定义
export type ExecutorType = 'web' | 'element-plus';

// Hooks 配置接口
export interface HooksConfig {
  beforeAll?: TestStep[];    // 套件开始前执行一次
  afterAll?: TestStep[];     // 套件结束后执行一次
  beforeEach?: TestStep[];   // 每个测试前执行
  afterEach?: TestStep[];    // 每个测试后执行
}

// 统一的测试运行器选项（合并原TestRunnerOptions）
export interface TestRunnerOptions {
  // 基础配置
  headless?: boolean;
  slowMo?: number;
  timeout?: number;
  retries?: number;
  continueOnFailure?: boolean;
  captureScreenshots?: boolean;
  outputDir?: string;
  browser?: 'chromium';

  // 模板配置
  templateDirs?: string[];
  templatePatterns?: string[];

  // CDP 连接相关选项
  debuggingPort?: number;
  cdpEndpoint?: string;
  autoPort?: boolean;
  connectionTimeout?: number;
  enableCDP?: boolean;
}

// 测试套件配置（统一原SuiteConfig）
export interface TestSuiteConfig {
  name: string;
  description?: string;
  baseUrl?: string;
  timeout?: number;
  retries?: number;
  parallel?: boolean;
  testMode?: 'flow' | 'boundary';
  captureScreenshots?: boolean;
  continueOnFailure?: boolean;
  variables?: Record<string, any>;
  dataSources?: DataSourceConfig[];   // 多个数据源
  hooks?: HooksConfig;
  executorType?: ExecutorType;            // 全局执行器类型
}

// 单个测试配置
export interface TestConfig {
  id: string;
  name: string;
  description?: string;
  tags?: string[];
  variables?: Record<string, any>;
  executorType?: ExecutorType;  // 测试级别的执行器配置
  timeout?: number;
  retries?: number;
}

export interface DataSourceConfig {
  name: string;                       // 响应数据对应的变量名
  type: 'api' | 'mock';
  config: {
    url?: string;                     // API数据源的URL，支持变量替换
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
    headers?: Record<string, string>;
    body?: any;
    timeout?: number;
    data?: any;                       // mock数据源的数据
  };
}


// Playwright Role Types
export type PlaywrightRole =
  "alert"|"alertdialog"|"application"|"article"|"banner"|"blockquote"|"button"|"caption"|"cell"|"checkbox"|"code"|"columnheader"|"combobox"|"complementary"|"contentinfo"|"definition"|"deletion"|"dialog"|"directory"|"document"|"emphasis"|"feed"|"figure"|"form"|"generic"|"grid"|"gridcell"|"group"|"heading"|"img"|"insertion"|"link"|"list"|"listbox"|"listitem"|"log"|"main"|"marquee"|"math"|"meter"|"menu"|"menubar"|"menuitem"|"menuitemcheckbox"|"menuitemradio"|"navigation"|"none"|"note"|"option"|"paragraph"|"presentation"|"progressbar"|"radio"|"radiogroup"|"region"|"row"|"rowgroup"|"rowheader"|"scrollbar"|"search"|"searchbox"|"separator"|"slider"|"spinbutton"|"status"|"strong"|"subscript"|"superscript"|"switch"|"tab"|"table"|"tablist"|"tabpanel"|"term"|"textbox"|"time"|"timer"|"toolbar"|"tooltip"|"tree"|"treegrid"|"treeitem";

// Playwright getByRole options interface
export interface RoleOptions {
  /**
   * An attribute that is usually set by `aria-checked` or native `<input type=checkbox>` controls.
   * Learn more about [`aria-checked`](https://www.w3.org/TR/wai-aria-1.2/#aria-checked).
   */
  checked?: boolean;

  /**
   * An attribute that is usually set by `aria-disabled` or `disabled`.
   * **NOTE** Unlike most other attributes, `disabled` is inherited through the DOM hierarchy.
   * Learn more about [`aria-disabled`](https://www.w3.org/TR/wai-aria-1.2/#aria-disabled).
   */
  disabled?: boolean;

  /**
   * Whether [`name`](https://playwright.dev/docs/api/class-page#page-get-by-role-option-name) is matched exactly:
   * case-sensitive and whole-string. Defaults to false. Ignored when
   * [`name`](https://playwright.dev/docs/api/class-page#page-get-by-role-option-name) is a regular expression.
   * Note that exact match still trims whitespace.
   */
  exact?: boolean;

  /**
   * An attribute that is usually set by `aria-expanded`.
   * Learn more about [`aria-expanded`](https://www.w3.org/TR/wai-aria-1.2/#aria-expanded).
   */
  expanded?: boolean;

  /**
   * Option that controls whether hidden elements are matched. By default, only non-hidden elements, as
   * [defined by ARIA](https://www.w3.org/TR/wai-aria-1.2/#tree_exclusion), are matched by role selector.
   * Learn more about [`aria-hidden`](https://www.w3.org/TR/wai-aria-1.2/#aria-hidden).
   */
  includeHidden?: boolean;

  /**
   * A number attribute that is usually present for roles `heading`, `listitem`, `row`, `treeitem`, with default values
   * for `<h1>-<h6>` elements.
   * Learn more about [`aria-level`](https://www.w3.org/TR/wai-aria-1.2/#aria-level).
   */
  level?: number;

  /**
   * Option to match the [accessible name](https://w3c.github.io/accname/#dfn-accessible-name). By default, matching is
   * case-insensitive and searches for a substring, use
   * [`exact`](https://playwright.dev/docs/api/class-page#page-get-by-role-option-exact) to control this behavior.
   * Learn more about [accessible name](https://w3c.github.io/accname/#dfn-accessible-name).
   */
  name?: string | RegExp;

  /**
   * An attribute that is usually set by `aria-pressed`.
   * Learn more about [`aria-pressed`](https://www.w3.org/TR/wai-aria-1.2/#aria-pressed).
   */
  pressed?: boolean;

  /**
   * An attribute that is usually set by `aria-selected`.
   * Learn more about [`aria-selected`](https://www.w3.org/TR/wai-aria-1.2/#aria-selected).
   */
  selected?: boolean;
}

/**
 * 上下文容器定位配置
 * 用于指定元素查找的容器范围
 */
export interface WithinContext {
  // Element locator fields (role is preferred over selector)
  role?: PlaywrightRole;
  roleOptions?: RoleOptions; // Options for role-based locators
  selector?: string; // CSS selector (fallback)
}

// Test Step Types
export interface TestStep {
  id: string;
  name: string;
  type?:
    | 'action'
    | 'assertion'
    | 'api'
    | 'custom'
    | 'template'
    | 'element'
    | 'text'
    | 'count'
    | 'attribute'
    | 'page'
    | 'class';

  // Element locator fields (role is preferred over selector)
  role?: PlaywrightRole;
  roleOptions?: RoleOptions; // Options for role-based locators
  selector?: string; // CSS selector (fallback)

  // Context container for element search
  within?: WithinContext; // Container to search within

  action: ActionType;
  data?: any;
  expected?: any;
  timeout?: number;
  retry?: RetryConfig;
  options?: StepOptions;

  // Additional fields for specific actions
  value?: any; // For fill actions
  url?: string; // For navigate actions
  state?: ElementState; // For waitForSelector actions
  variable?: string; // For extract actions
  attribute?: string; // For extractAttribute actions
  template?: string; // For useTemplate actions
  parameters?: Record<string, any>; // For template parameters
  steps?: TestStep[]; // For conditional/loop actions
  script?: string; // For executeScript action

  // Form interaction fields
  label?: string; // For selectOption with label
  index?: number; // For selectOption with index
  files?: string | string[]; // For setInputFiles action - 文件名（不包含路径），将从 .automation/static 目录中查找

  // Wait action fields
  duration?: number; // For wait action

  // Verification fields
  assertion?: string; // For verify actions (equals, contains, visible, etc.)
  property?: string; // For page verification (title, url, etc.)

  // Data extraction fields
  variables?: Record<string, string>; // For extracting multiple variables

  // Conditional execution fields
  when?: string; // Step-level condition - execute only if true
  condition?: string; // For conditional actions (deprecated, use 'if')
  if?: string; // For condition action
  then?: TestStep[]; // Steps to execute if condition is true
  else?: TestStep[]; // Steps to execute if condition is false

  // Loop execution fields
  items?: string | any[]; // For forEach actions - array or variable path
  item?: string; // Variable name for current item in forEach
  indexVar?: string; // Variable name for current index in forEach
}

export interface StepOptions {
  timeout?: number;
  hover?: boolean;
  force?: boolean;
  clear?: boolean;
  position?: { x: number; y: number };
  waitUntil?: 'load' | 'domcontentloaded' | 'networkidle';
  quality?: number;
  fullPage?: boolean; // For screenshot actions
  /** 页面上下文（页面调用时使用） */
  pageContext?: import('../types/page-api').PageContext;
}

export interface RetryConfig {
  attempts: number;
  delay: number;
  conditions?: string[];
}

export type ActionType =
  // Navigation actions
  | 'navigate'
  | 'goBack'
  | 'goForward'
  | 'reload'

  // Element interaction actions
  | 'click'
  | 'dblclick'
  | 'rightClick'
  | 'hover'

  // Form actions
  | 'fill'
  | 'selectOption'
  | 'selectDate'
  | 'check'
  | 'uncheck'
  | 'setInputFiles'

  // Wait actions
  | 'wait'
  | 'waitForSelector'
  | 'waitForText'
  | 'waitForResponse'
  | 'waitForLoadState'

  // Verification actions
  | 'verify'

  // Data extraction actions
  | 'extract'
  | 'extractText'
  | 'extractAttribute'
  | 'extractApiResponse'
  | 'setVariable'

  // Control flow actions
  | 'condition'
  | 'conditional' // deprecated, use 'condition'
  | 'forEach'
  | 'forEachElement'

  // Template actions
  | 'useTemplate'
  | 'useScript'

  // Utility actions
  | 'screenshot'
  | 'scroll'
  | 'executeScript';

export type ElementState = 'visible' | 'hidden' | 'attached' | 'detached';

export type AssertionType =
  | 'visible'
  | 'hidden'
  | 'enabled'
  | 'disabled'
  | 'checked'
  | 'equals'
  | 'contains'
  | 'startsWith'
  | 'endsWith'
  | 'matches'
  | 'greaterThan'
  | 'lessThan';

export type PageProperty = 'title' | 'url';

// 移除了未实现的数据收集和组件分析类型

// Template Types
export interface Template {
  name: string;
  type: 'shared' | 'business';
  description?: string;
  parameters: TemplateParameter[];
  steps: TestStep[];
  dependencies?: string[];
}

export interface TemplateParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required?: boolean;
  default?: any;
  description?: string;
}

// 移除了未实现的LLM和缓存类型

// Reporter Types
export interface TestResult {
  id: string;
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  screenshots?: string[];
  steps: StepResult[];
}

export interface StepResult {
  id: string;
  name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error?: string;
  data?: any;
}

export interface TestReport {
  summary: TestSummary;
  results: TestResult[];
  timestamp: number;
  environment: Record<string, any>;
}

export interface TestSummary {
  total: number;
  passed: number;
  failed: number;
  skipped: number;
  duration: number;
}

// Error Types
export class TestFrameworkError extends Error {
  constructor(
    message: string,
    public code?: string,
    public context?: any,
  ) {
    super(message);
    this.name = 'TestFrameworkError';
  }
}

export class ConfigurationError extends TestFrameworkError {
  constructor(message: string, context?: any) {
    super(message, 'CONFIG_ERROR', context);
    this.name = 'ConfigurationError';
  }
}

export class ExecutionError extends TestFrameworkError {
  constructor(message: string, context?: any) {
    super(message, 'EXECUTION_ERROR', context);
    this.name = 'ExecutionError';
  }
}

export type PageEx = Page & {
  _snapshotForAI: () => Promise<string>;
};
