# 模板系统详解

## 概述

模板系统是自动化测试框架的核心功能之一，提供了可复用的测试组件机制。通过模板系统，用户可以将常用的测试步骤封装成模板，实现测试用例的模块化和标准化。

## 核心设计原则

### 1. 模板优先工作流
- **强制验证**：所有模板必须先通过 `executeTemplateTest()` 验证
- **渐进式构建**：模板验证 → 完整YAML生成 → 最终执行
- **复用优先**：优先使用已验证的模板，避免重复定义

### 2. 优先级机制
1. **内联模板**（最高优先级）- 当前YAML文件中定义的模板
2. **全局模板**（次优先级）- 模板目录中注册的模板

### 3. 参数化设计
- 支持 `{{parameterName}}` 语法的参数占位符
- 类型安全的参数验证
- 嵌套参数访问支持

## 核心组件架构

### 1. TemplateRegistry（模板注册表）
**职责**：全局模板的注册、存储和管理

```typescript
interface TemplateDefinition {
  id: string;
  name: string;
  type: 'universal' | 'business';
  description?: string;
  parameters: TemplateParameter[];
  steps: TestStep[];
  dependencies?: string[];
}
```

**核心功能**：
- 模板注册和注销
- 依赖关系验证
- 循环依赖检测
- 模板查找和获取

### 2. TemplateResolver（模板解析器）
**职责**：按优先级查找和解析模板

```typescript
interface TemplateResolutionResult {
  template: TemplateDefinition | null;
  source: 'inline' | 'global' | 'none';
  templateId: string;
}
```

**解析策略**：
1. 优先查找内联模板
2. 回退到全局注册模板
3. 返回未找到结果

### 3. TemplateLoader（模板加载器）
**职责**：模板文件的加载和解析

**支持的文件格式**：
- `.yml` / `.yaml` - YAML格式模板文件
- 支持多模板文件组织

### 4. TemplateFactory（模板工厂）
**职责**：统一管理模板系统的初始化和协调

```typescript
class TemplateFactory {
  async initialize(): Promise<void>;
  getRegistry(): TemplateRegistry;
  getLoader(): TemplateLoader;
  async loadTemplate(templateId: string): Promise<UnresolvedTemplate>;
}
```

## 模板定义格式

### 1. 内联模板定义
```yaml
templates:
  search-form-template:
    name: "搜索表单模板"
    type: "business"
    description: "通用的搜索表单填写模板"
    parameters:
      - name: modelNumber
        type: string
        required: true
        description: "型号参数"
      - name: sellCarNameOrCode
        type: string
        required: false
        description: "车辆名称或编码"
    steps:
      - action: selectOption
        role: combobox
        roleOptions:
          name: "型号"
        data: "{{modelNumber}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "车辆名称/编码"
        data: "{{sellCarNameOrCode}}"
      - action: click
        role: button
        roleOptions:
          name: "查询"
```

### 2. 全局模板文件
```yaml
# templates/common/form-operations.yml
templates:
  login-template:
    name: "登录模板"
    type: "universal"
    parameters:
      - name: username
        type: string
        required: true
      - name: password
        type: string
        required: true
    steps:
      - action: fill
        role: textbox
        roleOptions:
          name: "用户名"
        data: "{{username}}"
      - action: fill
        role: textbox
        roleOptions:
          name: "密码"
        data: "{{password}}"
      - action: click
        role: button
        roleOptions:
          name: "登录"
```

## 模板使用方式

### 1. 模板调用语法
```yaml
tests:
  - name: "用户登录测试"
    steps:
      - action: useTemplate
        template: "login-template"
        parameters:
          username: "testuser"
          password: "testpass"
```

### 2. 模板验证调用
```typescript
// 模板验证示例
const templateYaml = `
templates:
  search-form-template:
    name: "搜索表单模板"
    parameters:
      - name: modelNumber
        type: string
      - name: sellCarNameOrCode
        type: string
    steps:
      - action: selectOption
        role: combobox
        roleOptions:
          name: "型号"
        data: "{{modelNumber}}"
`;

const parameters = {
  "search-form-template": {
    modelNumber: "XH01",
    sellCarNameOrCode: "12323"
  }
};

await executeTemplateTest(templateYaml, parameters);
```

## 模板处理流程

### 1. 模板注册流程
```
扫描模板目录 → 解析模板文件 → 验证模板格式 → 检查依赖关系 → 注册到Registry
```

### 2. 模板解析流程
```
接收模板ID → 查找内联模板 → 查找全局模板 → 返回解析结果
```

### 3. 模板执行流程
```
解析模板 → 验证参数 → 替换占位符 → 执行模板步骤 → 返回执行结果
```

## 参数系统

### 1. 参数定义
```typescript
interface TemplateParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required?: boolean;
  default?: any;
  description?: string;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    enum?: any[];
  };
}
```

### 2. 参数验证
- **类型检查**：确保参数类型匹配
- **必需性验证**：检查必需参数是否提供
- **格式验证**：支持正则表达式验证
- **范围验证**：数值范围和长度限制

### 3. 参数解析
```typescript
// 支持嵌套参数访问
"{{user.profile.name}}"
"{{config.baseUrl}}"
"{{formData.searchCriteria.modelNumber}}"
```

## 模板类型系统

### 1. Universal Templates（通用模板）
- **特点**：跨项目可复用的通用操作
- **示例**：登录、导航、基础表单操作
- **位置**：`templates/universal/`

### 2. Business Templates（业务模板）
- **特点**：特定业务场景的专用模板
- **示例**：特定表单填写、业务流程操作
- **位置**：`templates/business/`

## 依赖管理

### 1. 依赖声明
```yaml
templates:
  complex-workflow:
    name: "复杂工作流"
    dependencies:
      - "login-template"
      - "navigation-template"
    steps:
      - action: useTemplate
        template: "login-template"
        parameters: { ... }
      - action: useTemplate
        template: "navigation-template"
        parameters: { ... }
```

### 2. 循环依赖检测
```typescript
// 自动检测并阻止循环依赖
Template A → Template B → Template C → Template A  // ❌ 循环依赖
```

### 3. 依赖解析顺序
1. 解析所有依赖模板
2. 按依赖关系排序
3. 递归执行依赖链

## 错误处理

### 1. 模板验证错误
```typescript
{
  type: 'TEMPLATE_VALIDATION',
  severity: 'ERROR',
  message: '模板参数验证失败: 缺少必需参数 username',
  templateId: 'login-template',
  suggestions: ['检查模板调用时是否提供了所有必需参数']
}
```

### 2. 模板解析错误
```typescript
{
  type: 'TEMPLATE_RESOLUTION',
  severity: 'ERROR',
  message: '模板未找到: unknown-template',
  suggestions: [
    '检查模板ID是否正确',
    '确认模板已正确注册',
    '检查模板文件是否存在'
  ]
}
```

### 3. 循环依赖错误
```typescript
{
  type: 'TEMPLATE_DEPENDENCY',
  severity: 'ERROR',
  message: '检测到循环依赖: template-a → template-b → template-a',
  suggestions: ['重新设计模板依赖关系，避免循环引用']
}
```

## 最佳实践

### 1. 模板设计原则
- **单一职责**：每个模板专注于一个特定功能
- **参数化**：将可变部分抽象为参数
- **可复用性**：设计时考虑跨场景复用
- **文档化**：提供清晰的描述和参数说明

### 2. 命名规范
- **模板ID**：使用 kebab-case，如 `login-form-template`
- **参数名**：使用 camelCase，如 `userName`, `searchCriteria`
- **文件名**：使用描述性名称，如 `user-management.yml`

### 3. 组织结构
```
templates/
├── universal/          # 通用模板
│   ├── auth.yml       # 认证相关
│   ├── navigation.yml # 导航相关
│   └── forms.yml      # 表单相关
└── business/          # 业务模板
    ├── user-mgmt.yml  # 用户管理
    ├── order-mgmt.yml # 订单管理
    └── report.yml     # 报表相关
```

### 4. 版本管理
- 使用语义化版本控制
- 向后兼容性考虑
- 废弃模板的迁移指南

---

*模板系统是框架的核心特性，通过合理使用模板可以大大提高测试用例的可维护性和复用性。*
