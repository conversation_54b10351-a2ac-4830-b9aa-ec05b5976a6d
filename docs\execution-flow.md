# 测试执行流程详解

## 概述

自动化测试框架的执行流程遵循严格的模板优先工作流，确保测试的可靠性和一致性。整个流程分为三个主要阶段：**模板验证阶段**、**完整YAML验证阶段**和**测试执行阶段**。

## 执行流程总览

### 阶段1：模板验证阶段（Template Validation Phase）
1. **获取YAML Schema** - 调用 `getYamlSchema()`
2. **页面分析** - 调用 `getSnapshotForAI()` 和 `getSourceCodeFile()`
3. **元素分析与动作映射** - 识别元素类型并映射相应动作
4. **生成模板YAML** - 创建参数化模板
5. **模板验证** - 调用 `executeTemplateTest()` 验证模板功能
6. **模板修复** - 如果验证失败，修复并重新验证

### 阶段2：完整YAML验证阶段（Complete YAML Validation Phase）
7. **生成完整YAML** - 基于验证通过的模板创建完整测试脚本
8. **YAML验证** - 调用 `validateYaml()` 进行语法和结构验证
9. **验证修复** - 如果验证失败，修复并重新验证

### 阶段3：测试执行阶段（Test Execution Phase）
10. **最终执行** - 调用 `executeYamlTest()` 执行完整测试

## 详细执行流程

### 1. 初始化阶段

#### 1.1 TestingEngine 初始化
```typescript
const testingEngine = new TestingEngine({
  connectionType: 'cdp',
  headless: false,
  debuggingPort: 9222,
  cdpEndpoint: 'http://localhost:9222',
  timeout: 30000
});
```

#### 1.2 服务组件初始化
- **ConfigService** - 加载配置，合并环境变量和默认值
- **TestService** - 初始化测试服务，准备执行环境
- **ResourceService** - 建立浏览器连接，准备页面资源
- **TemplateFactory** - 扫描并注册全局模板
- **ExecutorService** - 注册内置执行器（web, element-plus）

### 2. YAML解析与验证阶段

#### 2.1 YAML语法验证
```typescript
// DSLParser.parseWithValidation()
const parseResult = DSLParser.parseWithValidation(yamlContent);
if (!parseResult.isValid) {
  throw new Error(`YAML验证失败: ${errorMessages}`);
}
```

**验证层次：**
1. **YAML语法验证** - 使用 `yaml.parse()` 检查语法正确性
2. **JSON Schema验证** - 使用 AJV 验证结构合规性
3. **业务逻辑验证** - 检查步骤、模板、配置的逻辑一致性

#### 2.2 配置解析与合并
```typescript
// ConfigService.resolveAndValidateConfig()
const configResult = await configService.resolveAndValidateConfig(
  testSuite.config,
  options
);
```

**配置优先级（从高到低）：**
1. 运行时选项 (`options`)
2. YAML配置文件 (`testSuite.config`)
3. 环境变量 (`.env` 文件)
4. 系统默认值

### 3. 数据源初始化阶段

#### 3.1 数据源配置解析
```typescript
if (config.dataSources && config.dataSources.length > 0) {
  const dataSourceData = await dataSourceManager.fetchMultipleData(
    config.dataSources,
    config.variables || {}
  );
}
```

#### 3.2 并行数据获取
- **API数据源** - 发送HTTP请求获取数据
- **Mock数据源** - 返回配置中的模拟数据
- **变量解析** - 支持 `{{variable}}` 语法的动态变量替换
- **错误处理** - 超时控制和重试机制

### 4. 执行上下文创建阶段

#### 4.1 DataAccessor 初始化
```typescript
const dataAccessor = new DataAccessor(variables, config);
```

**支持的变量命名空间：**
- 普通变量：`{{variableName}}`
- 配置变量：`{{config.propertyName}}`（只读）
- 嵌套属性：`{{user.profile.name}}`

#### 4.2 模板解析器创建
```typescript
const templateResolver = new TemplateResolver(
  inlineTemplates,    // 内联模板（高优先级）
  globalRegistry      // 全局模板（低优先级）
);
```

### 5. 测试套件执行阶段

#### 5.1 Hooks 执行
```typescript
// 执行前置钩子
if (testSuite.hooks?.beforeAll) {
  await this.executeSteps(testSuite.hooks.beforeAll, context);
}
```

**支持的钩子类型：**
- `beforeAll` - 所有测试前执行
- `afterAll` - 所有测试后执行
- `beforeEach` - 每个测试前执行
- `afterEach` - 每个测试后执行

#### 5.2 测试用例调度
```typescript
for (const test of testSuite.tests) {
  const testResult = await this.executeTest(test, context);
  results.push(testResult);
}
```

### 6. 测试步骤执行阶段

#### 6.1 执行器选择
```typescript
const executorType = this.selectExecutor(
  availableExecutors,
  context,
  step
);
```

**选择策略：**
1. **上下文指定** - 使用 `context.config.executorType`
2. **动作映射** - 特定动作自动选择执行器
3. **智能回退** - 默认使用 `web` 执行器

#### 6.2 步骤类型处理

##### 普通步骤执行
```typescript
await executorService.executeStep(step, context, executorType);
```

##### 模板步骤执行
```typescript
if (step.action === 'useTemplate') {
  const resolution = templateResolver.resolveTemplate(step.template);
  const resolvedSteps = dataAccessor.resolveObject(
    resolution.template.steps,
    step.parameters
  );
  await this.executeSteps(resolvedSteps, context);
}
```

##### 循环步骤执行
```typescript
if (step.action === 'forEach') {
  const dataArray = dataAccessor.resolve(step.data);
  for (const item of dataArray) {
    const itemContext = { ...context, currentItem: item };
    await this.executeSteps(step.steps, itemContext);
  }
}
```

### 7. 执行器层处理

#### 7.1 Web执行器处理流程
```typescript
// WebExecutor.executeStep()
switch (step.action) {
  case 'click':
    await this.executeClick(step, page, context);
    break;
  case 'fill':
    await this.executeFill(step, page, context);
    break;
  case 'selectOption':
    await this.executeSelectOption(step, page);
    break;
  // ... 其他动作
}
```

#### 7.2 Element Plus执行器特化处理
```typescript
// ElementPlusExecutor.executeSelectOption()
// 禁用placeholder点击事件，解决role点击不生效问题
await page.evaluate(() => {
  const selectElements = document.querySelectorAll('.el-form-item:has(.el-select)');
  // ... Element Plus特定处理逻辑
});
```

### 8. 结果收集与报告

#### 8.1 步骤结果收集
```typescript
const stepResult: StepResult = {
  success: true,
  duration: endTime - startTime,
  action: step.action,
  screenshot: captureScreenshots ? screenshotPath : undefined
};
```

#### 8.2 测试结果汇总
```typescript
const testResult: TestResult = {
  success: allStepsSucceeded,
  summary: {
    total: totalTests,
    passed: passedTests,
    failed: failedTests,
    skipped: skippedTests,
    duration: totalDuration
  },
  results: stepResults,
  errors: collectedErrors
};
```

## 错误处理机制

### 1. 分层错误处理
- **语法错误** - YAML解析阶段捕获
- **配置错误** - 配置验证阶段捕获
- **执行错误** - 步骤执行阶段捕获
- **资源错误** - 资源管理层捕获

### 2. 错误恢复策略
- **继续执行** - `continueOnFailure: true` 时跳过失败步骤
- **重试机制** - 支持步骤级别的重试配置
- **优雅降级** - 关键错误时提供默认行为
- **资源清理** - 确保异常情况下的资源释放

### 3. 详细错误信息
```typescript
{
  type: 'EXECUTION',
  severity: 'ERROR',
  message: '元素未找到: button[data-test="submit"]',
  location: { line: 15, column: 8 },
  suggestions: [
    '检查选择器是否正确',
    '确认元素是否已加载',
    '尝试添加等待条件'
  ]
}
```

## 性能优化策略

### 1. 并发处理
- **数据源并行获取** - 多个API同时请求
- **模板并发验证** - 独立模板可并行验证
- **资源预加载** - 提前初始化常用资源

### 2. 缓存机制
- **模板缓存** - 已解析模板缓存复用
- **配置缓存** - 避免重复解析配置
- **执行器实例缓存** - 懒加载和实例复用

### 3. 内存管理
- **及时清理** - 测试完成后释放资源
- **弱引用** - 避免循环引用导致内存泄漏
- **分批处理** - 大量测试用例分批执行

---

*此执行流程文档详细描述了框架的完整执行路径，为开发者提供了深入理解框架工作原理的参考。*
