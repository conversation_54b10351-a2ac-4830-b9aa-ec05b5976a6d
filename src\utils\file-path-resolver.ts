import * as path from 'path';
import * as fs from 'fs';
import { logger } from './logger';

/**
 * 文件路径解析器
 * 负责将文件名解析为 .automation/static 目录下的完整路径
 */
export class FilePathResolver {
  private static readonly STATIC_DIR = '.automation/static';

  /**
   * 将文件名解析为完整的文件路径
   * @param filename 文件名（不包含路径）
   * @returns 完整的文件路径
   * @throws Error 如果文件不存在
   */
  static resolveFilePath(filename: string): string {
    if (!filename) {
      throw new Error('文件名不能为空');
    }

    // 确保文件名不包含路径分隔符（安全检查）
    if (filename.includes('/') || filename.includes('\\')) {
      throw new Error(`文件名不能包含路径分隔符: ${filename}`);
    }

    // 构建完整路径
    const fullPath = path.join(process.cwd(), this.STATIC_DIR, filename);

    // 检查文件是否存在
    if (!fs.existsSync(fullPath)) {
      throw new Error(`文件不存在: ${filename}，请确保文件已放置在 ${this.STATIC_DIR} 目录中`);
    }

    // 检查是否为文件（而非目录）
    const stats = fs.statSync(fullPath);
    if (!stats.isFile()) {
      throw new Error(`指定路径不是文件: ${filename}`);
    }

    logger.debug('文件路径解析成功', {
      filename,
      fullPath,
      size: stats.size
    });

    return fullPath;
  }

  /**
   * 解析多个文件路径
   * @param filenames 文件名数组
   * @returns 完整文件路径数组
   */
  static resolveMultipleFilePaths(filenames: string[]): string[] {
    return filenames.map(filename => this.resolveFilePath(filename));
  }

  /**
   * 检查静态文件目录是否存在
   * @returns 目录是否存在
   */
  static checkStaticDirExists(): boolean {
    const staticDirPath = path.join(process.cwd(), this.STATIC_DIR);
    return fs.existsSync(staticDirPath) && fs.statSync(staticDirPath).isDirectory();
  }

  /**
   * 获取静态文件目录中的所有文件列表
   * @returns 文件名数组
   */
  static getAvailableFiles(): string[] {
    const staticDirPath = path.join(process.cwd(), this.STATIC_DIR);
    
    if (!this.checkStaticDirExists()) {
      return [];
    }

    try {
      return fs.readdirSync(staticDirPath)
        .filter(item => {
          const itemPath = path.join(staticDirPath, item);
          return fs.statSync(itemPath).isFile();
        });
    } catch (error) {
      logger.warn('读取静态文件目录失败', { error });
      return [];
    }
  }

  /**
   * 获取静态文件目录的绝对路径
   * @returns 静态文件目录的绝对路径
   */
  static getStaticDirPath(): string {
    return path.join(process.cwd(), this.STATIC_DIR);
  }
}
