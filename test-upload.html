<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .file-input {
            margin: 10px 0;
        }
        .upload-button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .upload-button:hover {
            background-color: #0056b3;
        }
        .file-info {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>文件上传测试页面</h1>
    
    <div class="upload-section">
        <h2>单文件上传</h2>
        <input type="file" id="singleFile" class="file-input" />
        <button type="button" class="upload-button" onclick="uploadSingleFile()">上传单个文件</button>
        <div id="singleFileInfo" class="file-info" style="display: none;"></div>
    </div>

    <div class="upload-section">
        <h2>多文件上传</h2>
        <input type="file" id="multipleFiles" class="file-input" multiple />
        <button type="button" class="upload-button" onclick="uploadMultipleFiles()">上传多个文件</button>
        <div id="multipleFilesInfo" class="file-info" style="display: none;"></div>
    </div>

    <div class="upload-section">
        <h2>拖拽上传区域</h2>
        <div id="dropZone" style="border: 2px dashed #ccc; padding: 40px; text-align: center; cursor: pointer;">
            点击或拖拽文件到此区域
        </div>
        <input type="file" id="dropFiles" style="display: none;" multiple />
        <div id="dropFilesInfo" class="file-info" style="display: none;"></div>
    </div>

    <script>
        function uploadSingleFile() {
            const fileInput = document.getElementById('singleFile');
            const fileInfo = document.getElementById('singleFileInfo');
            
            if (fileInput.files.length > 0) {
                const file = fileInput.files[0];
                fileInfo.innerHTML = `
                    <strong>已选择文件:</strong><br>
                    文件名: ${file.name}<br>
                    文件大小: ${file.size} bytes<br>
                    文件类型: ${file.type}
                `;
                fileInfo.style.display = 'block';
            } else {
                alert('请先选择一个文件');
            }
        }

        function uploadMultipleFiles() {
            const fileInput = document.getElementById('multipleFiles');
            const fileInfo = document.getElementById('multipleFilesInfo');
            
            if (fileInput.files.length > 0) {
                let info = '<strong>已选择文件:</strong><br>';
                for (let i = 0; i < fileInput.files.length; i++) {
                    const file = fileInput.files[i];
                    info += `${i + 1}. ${file.name} (${file.size} bytes)<br>`;
                }
                fileInfo.innerHTML = info;
                fileInfo.style.display = 'block';
            } else {
                alert('请先选择文件');
            }
        }

        // 拖拽上传功能
        const dropZone = document.getElementById('dropZone');
        const dropFiles = document.getElementById('dropFiles');
        const dropFilesInfo = document.getElementById('dropFilesInfo');

        dropZone.addEventListener('click', () => {
            dropFiles.click();
        });

        dropFiles.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });

        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.style.backgroundColor = '#f0f0f0';
        });

        dropZone.addEventListener('dragleave', (e) => {
            e.preventDefault();
            dropZone.style.backgroundColor = '';
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.style.backgroundColor = '';
            handleFiles(e.dataTransfer.files);
        });

        function handleFiles(files) {
            if (files.length > 0) {
                let info = '<strong>拖拽文件:</strong><br>';
                for (let i = 0; i < files.length; i++) {
                    const file = files[i];
                    info += `${i + 1}. ${file.name} (${file.size} bytes)<br>`;
                }
                dropFilesInfo.innerHTML = info;
                dropFilesInfo.style.display = 'block';
            }
        }
    </script>
</body>
</html>
