# 自动化测试框架架构概览

## 项目简介

这是一个基于 Playwright 的现代化浏览器自动化测试框架，采用 TypeScript 开发，支持多种执行环境和测试场景。框架设计遵循 KISS 原则（Keep It Simple, Stupid），提供了完整的测试生命周期管理、模板化测试、数据驱动测试等功能。

## 核心设计原则

### 1. 模块化设计
- **单一职责原则**：每个模块专注于特定功能
- **依赖注入**：通过接口和服务注入实现松耦合
- **插件化架构**：支持执行器、模板、脚本等组件的扩展

### 2. 分层架构
```
表示层 (Presentation Layer)
├── TestingEngine (统一入口)
└── API Service (页面API注入)

业务逻辑层 (Business Logic Layer)
├── TestService (测试服务)
├── ConfigService (配置服务)
└── ExecutionEngine (执行引擎)

基础设施层 (Infrastructure Layer)
├── ExecutorService (执行器管理)
├── ResourceService (资源管理)
├── TemplateSystem (模板系统)
└── DataManagement (数据管理)

数据访问层 (Data Access Layer)
├── DSLParser (YAML解析)
├── YAMLValidator (验证器)
└── DataAccessor (数据访问器)
```

### 3. 配置优先级
1. **运行时选项** (最高优先级)
2. **YAML配置文件**
3. **环境变量**
4. **系统默认值** (最低优先级)

## 核心模块架构

### 1. 测试引擎层 (Testing Engine Layer)

#### TestingEngine
- **职责**：框架的统一入口点，提供简洁的4方法接口
- **核心方法**：
  - `runYamlFile()` - 执行YAML测试文件
  - `runYamlContent()` - 执行YAML内容
  - `executeStep()` - 执行单个测试步骤
  - `cleanup()` - 清理资源

#### TestService
- **职责**：统一业务逻辑处理，整合TestRunner和GlobalTestTaskManager功能
- **核心功能**：
  - 生命周期管理（hooks处理）
  - 数据源初始化
  - 执行上下文创建
  - 错误处理和恢复

### 2. 执行引擎层 (Execution Engine Layer)

#### ExecutionEngine
- **职责**：专门负责测试执行逻辑
- **核心功能**：
  - 测试套件执行
  - 测试用例调度
  - 步骤执行协调
  - 结果收集和汇总

#### ExecutorService
- **职责**：执行器管理和调度
- **支持的执行器类型**：
  - `web` - 标准Web执行器（默认）
  - `element-plus` - Element Plus组件特化执行器
- **核心功能**：
  - 执行器注册和发现
  - 动态执行器选择
  - 执行器生命周期管理

### 3. 模板系统 (Template System)

#### 模板优先级机制
1. **内联模板** (最高优先级) - 当前YAML文件中定义的模板
2. **全局模板** (次优先级) - 模板目录中注册的模板

#### 核心组件
- **TemplateRegistry** - 全局模板注册表
- **TemplateResolver** - 模板解析器，实现优先级查找
- **TemplateLoader** - 模板加载器
- **TemplateFactory** - 模板工厂，统一管理模板系统

### 4. 数据管理系统 (Data Management System)

#### DataAccessor
- **职责**：统一的数据访问接口
- **支持的命名空间**：
  - 普通变量：`{{variableName}}`
  - 配置变量：`{{config.propertyName}}`（只读）
- **核心功能**：
  - 变量解析和替换
  - 嵌套属性访问
  - 类型保持（纯变量引用）

#### DataSourceManager
- **职责**：数据源管理和数据获取
- **支持的数据源类型**：
  - `api` - REST API数据源
  - `mock` - 模拟数据源
- **核心功能**：
  - 并行数据获取
  - 变量解析
  - 错误处理和重试

### 5. 验证系统 (Validation System)

#### YAMLSyntaxValidator
- **职责**：YAML语法和结构验证
- **验证层次**：
  1. YAML语法验证
  2. JSON Schema验证
  3. 业务逻辑验证

#### YAMLSchemaExporter
- **职责**：Schema定义和导出
- **导出格式**：
  - JSON Schema（标准格式）
  - LLM优化格式（包含示例和说明）

## 关键设计决策

### 1. 模板优先工作流
- **强制模板验证**：所有模板必须先验证后使用
- **模板复用优先**：优先使用已验证的模板而非重新定义
- **渐进式构建**：模板 → 完整YAML → 执行

### 2. 执行器选择策略
- **上下文优先**：根据配置中的executorType选择
- **动作映射**：特定动作自动选择对应执行器
- **智能回退**：不可用时自动选择替代执行器

### 3. 数据流设计
- **预加载机制**：数据源在测试执行前完成加载
- **变量作用域**：支持全局、测试、步骤级别的变量
- **类型保持**：变量解析时保持原始数据类型

### 4. 错误处理策略
- **分层错误处理**：每层都有对应的错误处理机制
- **优雅降级**：关键错误时提供默认行为
- **详细错误信息**：提供具体的错误位置和修复建议

## 扩展点

### 1. 执行器扩展
- 实现执行器接口
- 注册到ExecutorService
- 支持特定UI框架或平台

### 2. 数据源扩展
- 实现数据源接口
- 添加到DataSourceManager
- 支持新的数据获取方式

### 3. 验证器扩展
- 扩展YAMLSyntaxValidator
- 添加自定义验证规则
- 支持特定业务逻辑验证

### 4. 模板类型扩展
- 定义新的模板类型
- 实现模板处理逻辑
- 支持特定业务场景

## 性能优化

### 1. 懒加载机制
- 执行器按需创建
- 模板按需加载
- 资源延迟初始化

### 2. 并发处理
- 数据源并行获取
- 模板并发验证
- 测试用例并行执行（可配置）

### 3. 资源管理
- 自动资源清理
- 浏览器实例复用
- 内存使用优化

## 下一步发展方向

1. **AI集成增强** - 更深度的AI辅助测试生成
2. **可视化界面** - 提供图形化的测试编辑和监控界面
3. **云端执行** - 支持分布式测试执行
4. **更多执行器** - 支持移动端、桌面应用等平台
5. **高级分析** - 测试结果分析和报告生成

---

*本文档将随着框架的发展持续更新，确保架构文档与实际实现保持同步。*
